package com.amobilab.ezmath.ai.data.network.services

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.openai.api.chat.ChatCompletion
import amobi.module.openai.api.chat.ChatCompletionRequest
import amobi.module.openai.api.chat.ChatMessage
import amobi.module.openai.api.chat.ChatRole
import amobi.module.openai.api.chat.ImagePart
import amobi.module.openai.api.chat.StreamOptions
import amobi.module.openai.api.chat.TextPart
import amobi.module.openai.api.model.ModelId
import amobi.module.openai.client.OpenAI
import amobi.module.openai.client.OpenAIHost
import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.AppCheckUtils
import com.amobilab.ezmath.ai.values.Const
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

class GatewayApi_backup {
    companion object {
        private const val END_POINT = "http://192.168.1.12:8787/v1/"
        private fun getModelName():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) {
                Const.AiModelName.GPT
            } else {
                Const.AiModelName.GEMINI
            }
        }
        private fun getPortkeyProvider():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) "openai"
            else "gemini"
        }
    }

    private val isCollecting = MutableStateFlow(true)

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponsesNoStream(prompts: String, bitmap: Bitmap? = null, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            return ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = END_POINT),
            headers = mutableMapOf(
                "x-portkey-provider" to "openai",
                "x-gemini-model-id" to getModelName(),
                "x-provider-bacha" to getPortkeyProvider(),
                "Content-Type" to "application/json",
                "X-Firebase-AppCheck" to appCheckToken,
            )
        )

        debugLog("aaaaaaaaaa headers: ${mutableMapOf(
            "x-portkey-provider" to "openai",
            "x-gemini-model-id" to getModelName(),
            "x-provider-bacha" to getPortkeyProvider(),
            "Content-Type" to "application/json",
            "X-Firebase-AppCheck" to appCheckToken,
        )}")

        return try {
            val messages = mutableListOf(
                ChatMessage(
                    role = ChatRole.System,
                    content = systemInstruction
                )
            )

            // Add user message with image if bitmap is provided
            if (bitmap != null) {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = listOf(
                            TextPart(text = prompts),
                            ImagePart(url = "data:image/png;base64," + encodeToBase64(bitmap), detail = "low")
                        )
                    )
                )
            } else {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = prompts
                    )
                )
            }

            val chatCompletionRequest = ChatCompletionRequest(
                model = ModelId(getModelName()),
                messages = messages
            )

            val completion: ChatCompletion = openAI.chatCompletion(chatCompletionRequest)

            val content = completion.choices.firstOrNull()?.message?.content ?: ""
            val promptTokens = completion.usage?.promptTokens ?: 0
            val completionTokens = completion.usage?.completionTokens ?: 0
            val totalTokens = completion.usage?.totalTokens ?: 0

            ChatResponse(
                chat = Chat(
                    prompt = content,
                    bitmap = null,
                    isFromUser = false,
                    isError = false,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = promptTokens,
                outputTokenCount = completionTokens,
                totalTokenCount = totalTokens
            )
        } catch (e: Exception) {
            debugLog(" ${e.message}")
            ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    private fun encodeToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.NO_WRAP)
    }
}
