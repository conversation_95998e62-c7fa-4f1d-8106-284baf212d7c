package com.amobilab.ezmath.ai.data.network.services

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.AppCheckUtils
import com.amobilab.ezmath.ai.values.Const
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.Headers
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.ByteArrayOutputStream

class GatewayApi {
    companion object {
        private val client = OkHttpClient()
        private const val END_POINT = "http://192.168.1.12:8787/v1/"
        private fun getModelName():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) {
                Const.AiModelName.GPT
            } else {
                Const.AiModelName.GEMINI
            }
        }
        private fun getPortkeyProvider():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) "openai"
            else "gemini"
        }
    }

    private val isCollecting = MutableStateFlow(true)

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponsesNoStream(prompts: String, bitmap: Bitmap? = null, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            return ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }

        val headers = Headers.Builder()
            .add("x-portkey-provider", "openai")
            .add("x-gemini-model-id", getModelName())
            .add("x-provider-bacha", getPortkeyProvider())
            .add("Content-Type", "application/json")
            .add("X-Firebase-AppCheck", appCheckToken)
            .build()

        debugLog("aaaaaaaaaa headers: ${mapOf(
            "x-portkey-provider" to "openai",
            "x-gemini-model-id" to getModelName(),
            "x-provider-bacha" to getPortkeyProvider(),
            "Content-Type" to "application/json",
            "X-Firebase-AppCheck" to appCheckToken,
        )}")

        return try {
            val messageList = JSONArray().apply {
                // System message
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })

                // User message with image if bitmap is provided
                if (bitmap != null) {
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", JSONArray().apply {
                            put(JSONObject().apply {
                                put("type", "text")
                                put("text", prompts)
                            })
                            put(JSONObject().apply {
                                put("type", "image_url")
                                put("image_url", JSONObject().apply {
                                    put("url", "data:image/png;base64," + encodeToBase64(bitmap))
                                    put("detail", "low")
                                })
                            })
                        })
                    })
                } else {
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", prompts)
                    })
                }
            }

            val payload = JSONObject().apply {
                put("model", getModelName())
                put("messages", messageList)
                put("stream", false) // No streaming for this function
            }

            val request = Request.Builder()
                .url(END_POINT + "chat/completions")
                .headers(headers)
                .post(payload.toString().toRequestBody("application/json".toMediaType()))
                .build()

            val response = withContext(Dispatchers.IO) {
                client.newCall(request).execute()
            }

            if (response.isSuccessful) {
                val responseBody = response.body.string()
                val jsonResponse = JSONObject(responseBody)
                
                val content = jsonResponse.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content")
                
                val usage = jsonResponse.optJSONObject("usage")
                val promptTokens = usage?.optInt("prompt_tokens", 0) ?: 0
                val completionTokens = usage?.optInt("completion_tokens", 0) ?: 0
                val totalTokens = usage?.optInt("total_tokens", 0) ?: 0

                ChatResponse(
                    chat = Chat(
                        prompt = content,
                        bitmap = null,
                        isFromUser = false,
                        isError = false,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = promptTokens,
                    outputTokenCount = completionTokens,
                    totalTokenCount = totalTokens
                )
            } else {
                ChatResponse(
                    chat = Chat(
                        prompt = "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            }
        } catch (e: Exception) {
            debugLog(" ${e.message}")
            ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    fun getResponseWithImage(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        mode: ChatQuestionMode
    ): Flow<ChatResponse> = flow {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        val headers = Headers.Builder()
            .add("x-portkey-provider", "openai")
            .add("x-gemini-model-id", getModelName())
            .add("x-provider-bacha", getPortkeyProvider())
            .add("Content-Type", "application/json")
            .add("X-Firebase-AppCheck", appCheckToken)
            .build()

        var isSentError = true
        try {
            val promptImage = when (mode) {
                ChatQuestionMode.Translate ->
                    context.getString(mode.promptImageId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

                else -> context.getString(mode.promptImageId)
            }

            val messageList = JSONArray().apply {
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", JSONArray().apply {
                        put(JSONObject().apply {
                            put("type", "text")
                            put("text", "$promptImage: $prompt")
                        })
                        put(JSONObject().apply {
                            put("type", "image_url")
                            put("image_url", JSONObject().apply {
                                put("url", "data:image/png;base64," + encodeToBase64(bitmap))
                                put("detail", "low")
                            })
                        })
                    })
                })
            }

            val payload = JSONObject().apply {
                put("model", getModelName())
                put("messages", messageList)
                put("stream", true)
                put("max_tokens", Const.MAX_OUTPUT_TOKENS)
            }

            val request = Request.Builder()
                .url(END_POINT + "chat/completions")
                .headers(headers)
                .post(payload.toString().toRequestBody("application/json".toMediaType()))
                .build()

            val response = withTimeoutOrNull(30_000) {
                withContext(Dispatchers.IO) {
                    client.newCall(request).execute()
                }
            }

            response?.let { httpResponse ->
                if (httpResponse.isSuccessful) {
                    val responseBody = httpResponse.body.byteStream()

                    responseBody.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.isNotEmpty()) {
                                val content = parseChatCompletionResult(line)
                                if (content != null && content.isNotEmpty()) {
                                    isSentError = false

                                    emit(
                                        ChatResponse(
                                            chat = Chat(
                                                prompt = content,
                                                bitmap = null,
                                                isFromUser = false,
                                                isError = false,
                                                botType = BotType.BOT_GPT
                                            ),
                                            inputTokenCount = 0,
                                            outputTokenCount = 0,
                                            totalTokenCount = 0
                                        )
                                    )
                                }
                            }
                        }
                    }
                } else {
                    emit(
                        ChatResponse(
                            chat = Chat(
                                prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                                bitmap = null,
                                isFromUser = false,
                                isError = true,
                                botType = BotType.BOT_GPT
                            ),
                            inputTokenCount = 0,
                            outputTokenCount = 0,
                            totalTokenCount = 0
                        )
                    )
                }
            } ?: run {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            }

        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error: ${e.message}")
        emit(
            ChatResponse(
                chat = Chat(
                    prompt = context.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        )
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    fun getResponses(context: Context, listChat: MutableList<Chat>, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        val headers = Headers.Builder()
            .add("x-portkey-provider", "openai")
            .add("x-gemini-model-id", getModelName())
            .add("x-provider-bacha", getPortkeyProvider())
            .add("Content-Type", "application/json")
            .add("X-Firebase-AppCheck", appCheckToken)
            .build()

        isCollecting.value = true
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId) + ".\n" + "Returns the formula in KaTeX-compatible format"
        }

        var isSentError = true
        try {
            val messageList = JSONArray().apply {
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })

                listChat.take(6).reversed().forEach { chat ->
                    if (chat.isFromUser) {
                        if (chat.bitmap != null) {
                            put(JSONObject().apply {
                                put("role", "user")
                                put("content", JSONArray().apply {
                                    put(JSONObject().apply {
                                        put("type", "text")
                                        put("text", chat.prompt)
                                    })
                                    put(JSONObject().apply {
                                        put("type", "image_url")
                                        put("image_url", JSONObject().apply {
                                            put("url", "data:image/png;base64," + encodeToBase64(chat.bitmap))
                                            put("detail", "low")
                                        })
                                    })
                                })
                            })
                        } else {
                            put(JSONObject().apply {
                                put("role", "user")
                                put("content", chat.prompt)
                            })
                        }
                    } else {
                        put(JSONObject().apply {
                            put("role", "assistant")
                            put("content", chat.prompt)
                        })
                    }
                }
            }

            val payload = JSONObject().apply {
                put("model", getModelName())
                put("messages", messageList)
                put("stream", true)
                put("max_tokens", Const.MAX_OUTPUT_TOKENS)
            }

            val request = Request.Builder()
                .url(END_POINT + "chat/completions")
                .headers(headers)
                .post(payload.toString().toRequestBody("application/json".toMediaType()))
                .build()

            val response = withTimeoutOrNull(30_000) {
                withContext(Dispatchers.IO) {
                    client.newCall(request).execute()
                }
            }

            response?.let { httpResponse ->
                if (httpResponse.isSuccessful) {
                    val responseBody = httpResponse.body.byteStream()

                    responseBody.bufferedReader().useLines { lines ->
                        lines.forEach { line ->
                            if (line.isNotEmpty() && isCollecting.value) {
                                val content = parseChatCompletionResult(line)
                                if (content != null) {
                                    isSentError = false
                                    debugLog("response ${content}")

                                    emit(
                                        ChatResponse(
                                            chat = Chat(
                                                prompt = content,
                                                bitmap = null,
                                                isFromUser = false,
                                                isError = false,
                                                botType = BotType.BOT_GPT
                                            ),
                                            inputTokenCount = 0,
                                            outputTokenCount = 0,
                                            totalTokenCount = 0
                                        )
                                    )
                                }
                            }
                        }
                    }
                    debugLog("onCompletion : xong")
                } else {
                    emit(
                        ChatResponse(
                            chat = Chat(
                                prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                                bitmap = null,
                                isFromUser = false,
                                isError = true,
                                botType = BotType.BOT_GPT
                            ),
                            inputTokenCount = 0,
                            outputTokenCount = 0,
                            totalTokenCount = 0
                        )
                    )
                }
            } ?: run {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            }
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
            try {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            } catch (e: Exception) {
                debugLog("Response error stream: ${e.message}")
            }
            isCollecting.value = false
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error stream: ${e.message}")
        try {
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = context.getString(R.string.errors_please_try_again),
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
        }
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    private fun parseChatCompletionResult(jsonString: String): String? {
        try {
            val jsonData = jsonString.substringAfter(": ").trim()
            if (jsonData == "[DONE]"){
                return ""
            }
            val jsonObject = JSONObject(jsonData)

            val choicesArray = jsonObject.getJSONArray("choices")
            if (choicesArray.length() > 0) {
                val choicesObject = choicesArray.getJSONObject(0)

                if (choicesObject.has("delta")) {
                    val deltaObject = choicesObject.getJSONObject("delta")
                    val content = deltaObject.optString("content", "")
                    return content
                }

                val finishReason = choicesObject.optString("finish_reason", null)
                if (finishReason == "stop") {
                    return ""
                }
            }

            return ""
        } catch (e: JSONException) {
            e.printStackTrace()
            debugLog("Error parsing JSON: ${e.message}")
        }
        return null
    }

    private fun encodeToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.NO_WRAP)
    }
}
